import React, { useState, useId } from "react";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { BsSlack } from "react-icons/bs";
import { IoCall } from "react-icons/io5";
import { MdEmail } from "react-icons/md";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager, forceRestoreScroll } from "../utils/scrollManager";
import styles from "./Team.module.css";

const team = [
  {
    name: "<PERSON>",
    role: "Research Lead",
    imageUrl:
      "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "<PERSON> oversees the research process, coordinates the team, and ensures the project meets its objectives. She is responsible for methodology and final reporting.",
  },
  {
    name: "<PERSON>",
    role: "Senior Analyst",
    imageUrl:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "Michael analyzes collected data, identifies trends, and synthesizes insights. He is key in transforming raw data into actionable findings.",
  },
  {
    name: "Emily Rodriguez",
    role: "UX Researcher",
    imageUrl:
      "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "Emily conducts interviews and usability tests, focusing on user experience and pain points. She helps ensure the personas are grounded in real user needs.",
  },
  {
    name: "David Kim",
    role: "Data Analyst",
    imageUrl:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "David manages data collection and visualization, and supports the team with statistical analysis and reporting.",
  },
];

export function Team() {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const modalId = useId(); // Generate unique ID for this modal instance

  // Register team modal with overlay system
  useOverlayState(selectedIndex !== null);
  
  // Use the scroll manager to preserve scroll position (Team modal is detected as regular)
  useScrollManager(modalId, selectedIndex !== null, 'Team');

  const openModal = (index: number) => setSelectedIndex(index);
  const closeModal = () => {
    setSelectedIndex(null);
  };
  const showPrev = () =>
    setSelectedIndex((prev) =>
      prev !== null ? (prev - 1 + team.length) % team.length : null
    );
  const showNext = () =>
    setSelectedIndex((prev) =>
      prev !== null ? (prev + 1) % team.length : null
    );

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      closeModal();
    }
  };

  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedIndex !== null) {
        closeModal();
      }
    };

    if (selectedIndex !== null) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [selectedIndex]);

  // Safety cleanup on unmount
  React.useEffect(() => {
    return () => {
      // Force restore scroll as a safety measure on component unmount
      forceRestoreScroll();
    };
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>Research Team</h2>
        <p className={styles.subtitle}>
          Meet the people behind the insights
        </p>
      </div>

      <NeumorphicContainer>
        <div className={styles.teamGrid}>
          {team.map((member, index) => (
            <div
              key={index}
              className={styles.memberCard}
              onClick={() => openModal(index)}
            >
              <div className={styles.memberImageContainer}>
                <img
                  src={member.imageUrl}
                  alt={member.name}
                  className={styles.memberImage}
                />
              </div>
              <h3 className={styles.memberName}>
                {member.name}
              </h3>
              <p className={styles.memberRole}>{member.role}</p>
            </div>
          ))}
        </div>
      </NeumorphicContainer>

      {/* Modal for team member details with carousel */}
      {selectedIndex !== null && (
        <div className={styles.modalBackdrop} onClick={handleBackdropClick}>
          {/* Backdrop */}
          <div
            className={styles.modalOverlay}
          />

          {/* Modal Container */}
          <div
            className={styles.modal}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={closeModal}
              className={styles.modalCloseButton}
              aria-label='Close'
            >
              <X className={styles.modalCloseIcon} />
            </button>
            <img
              src={team[selectedIndex].imageUrl}
              alt={team[selectedIndex].name}
              className={styles.modalImage}
            />
            <h3 className={styles.modalName}>
              {team[selectedIndex].name}
            </h3>
            <p className={styles.modalRole}>
              {team[selectedIndex].role}
            </p>
            <p className={styles.modalDetails}>
              {team[selectedIndex].details}
            </p>
            <div className={styles.modalNavigation}>
              <button
                onClick={showPrev}
                className={styles.navButton}
                aria-label='Previous'
              >
                <ChevronLeft className={styles.navIcon} />
              </button>

              {/* Contact Icons Container - positioned between navigation buttons */}
              <div className={styles.contactIcons}>
                {/* Email Icon */}
                <button
                  onClick={() => console.log("Email clicked - placeholder")}
                  className={styles.contactButton}
                  aria-label='Send Email'
                  title='Send Email'
                >
                  <MdEmail className={styles.contactIcon} />
                </button>

                {/* X/Twitter Icon */}
                {/* <button
                  onClick={() => console.log("X/Twitter clicked - placeholder")}
                  className={styles.contactButton}
                  aria-label='View X Profile'
                  title='View X Profile'
                >
                  <FaXTwitter className={styles.contactIcon} />
                </button> */}

                {/* Slack Icon */}
                <button
                  onClick={() => console.log("Slack clicked - placeholder")}
                  className={styles.contactButton}
                  aria-label='Send Slack Message'
                  title='Send Slack Message'
                >
                  <BsSlack className={styles.contactIcon} />
                </button>

                {/* Teams Icon */}
                {/* <button
                  onClick={() => console.log("Teams clicked - placeholder")}
                  className={styles.contactButton}
                  aria-label='Send Teams Message'
                  title='Send Teams Message'
                >
                  <BsMicrosoftTeams className={styles.contactIcon} />
                </button> */}

                {/* Phone Icon */}
                <button
                  onClick={() => console.log("Phone clicked - placeholder")}
                  className={styles.contactButton}
                  aria-label='Call Phone'
                  title='Call Phone'
                >
                  <IoCall className={styles.contactIcon} />
                </button>
              </div>

              <button
                onClick={showNext}
                className={styles.navButton}
                aria-label='Next'
              >
                <ChevronRight className={styles.navIcon} />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
