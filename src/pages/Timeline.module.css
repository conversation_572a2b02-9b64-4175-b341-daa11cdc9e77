/* Timeline Page Styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  padding: var(--spacing-2);
}

.header {
  /* Header styles */
}

.title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.subtitle {
  color: var(--color-text-secondary);
  margin-top: var(--spacing-2);
  font-size: var(--font-size-lg);
}

/* Timeline Container */
.timelineContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-16);
}

/* First Row - Left to Right */
.firstRow {
  display: flex;
  align-items: center;
  padding-left: var(--spacing-12);
  padding-right: var(--spacing-12);
  padding-top: var(--spacing-16);
}

.timelineEvent {
  position: relative;
  display: flex;
  align-items: center;
}

.eventNode {
  width: var(--spacing-6);
  height: var(--spacing-6);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.eventNodeCompleted {
  background: linear-gradient(to right, #4ade80, #22c55e);
  box-shadow: 0 4px 6px -1px rgba(34, 197, 94, 0.1);
}

.eventNodeCurrent {
  background: linear-gradient(to right, #60a5fa, #3b82f6);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
}

.eventNodeUpcoming {
  background: white;
  border: 2px solid var(--color-gray-300);
}

.eventIcon {
  height: var(--spacing-4);
  width: var(--spacing-4);
  color: white;
}

.eventCurrentDot {
  width: var(--spacing-2);
  height: var(--spacing-2);
  border-radius: var(--radius-full);
  background: white;
}

.eventButton {
  position: absolute;
  top: -64px;
  left: 50%;
  transform: translateX(-50%);
  width: 128px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
}

.eventPhase {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  transition: color var(--transition-base);
}

.eventButton:hover .eventPhase {
  color: var(--color-text-accent);
}

.eventDate {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  margin-top: var(--spacing-1);
}

.connector {
  height: 3px;
  background: linear-gradient(to right, #4ade80, #22c55e);
  flex: 1;
  margin: 0;
}

.connectorDotted {
  height: 3px;
  border-bottom: 3px dotted white;
  background: none;
  flex: 1;
  margin: 0;
}

/* Second Row - Right to Left */
.secondRow {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  position: relative;
  padding-left: var(--spacing-12);
  padding-right: var(--spacing-12);
  padding-bottom: var(--spacing-16);
}

.verticalConnector {
  position: absolute;
  right: 59px;
  top: -75px;
  width: 3px;
  background: linear-gradient(to bottom, #22c55e, #3b82f6);
  height: 75px;
}

.secondRowEventButton {
  position: absolute;
  bottom: -64px;
  left: 50%;
  transform: translateX(-50%);
  width: 128px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
}

/* Modal Styles */
.modalBackdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal {
  /* background: #ffffff44; */
  backdrop-filter: blur(5px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 512px;
  width: 100%;
  margin: var(--spacing-4);
  position: relative;
  overflow: hidden;
}

.modalContent {
  padding: var(--spacing-8);
}

.modalCloseButton {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  padding: var(--spacing-2);
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.modalCloseButton:hover {
  background: var(--color-gray-100);
}

.modalCloseIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: var(--color-text-muted);
}

.modalTitle {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

.modalDate {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-6);
}

.modalDetails {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.modalDetailItem {
  display: flex;
  align-items: flex-start;
}

.modalDetailBullet {
  height: var(--spacing-2);
  width: var(--spacing-2);
  border-radius: var(--radius-full);
  background: var(--color-primary-600);
  margin-top: var(--spacing-2);
  margin-right: var(--spacing-3);
  flex-shrink: 0;
}

.modalDetailText {
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .firstRow,
  .secondRow {
    padding: 0 var(--spacing-4);
  }
  
  .eventButton,
  .secondRowEventButton {
    width: 96px;
  }
  
  .eventPhase {
    font-size: var(--font-size-xs);
  }
  
  .modalContent {
    padding: var(--spacing-6);
  }
}

/* Animation Classes */
.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.scaleIn {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
