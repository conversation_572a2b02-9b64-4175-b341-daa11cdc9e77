.container {
  padding: 2rem 0;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.timelineWrapper {
  position: relative;
  padding: 0 1rem;
}

.timelineLine {
  position: absolute;
  left: 2rem;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: #e5e7eb;
}

.timelineEvents {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.timelineEvent {
  position: relative;
  display: flex;
  align-items: center;
  opacity: 0;
}

.timelineEvent:hover .eventContent {
  transform: translateY(-0.25rem);
}

.eventMarker {
  position: absolute;
  left: 2rem;
  transform: translateX(-50%);
}

.completedMarker {
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background-color: #10b981;
  display: flex;
  align-items: center;
  justify-content: center;
}

.currentMarker {
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background-color: #3b82f6;
  box-shadow: 0 0 0 4px #dbeafe;
}

.futureMarker {
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  background-color: #ffffff;
}

.eventContent {
  margin-left: 4rem;
  transform: translateY(0);
  transition: transform 0.2s ease-in-out;
}

.eventTitle {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
}

.eventDate {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.checkIcon {
  height: 0.75rem;
  width: 0.75rem;
  color: white;
}

/* Animation classes */
.fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.delay1 { animation-delay: 0.2s; }
.delay2 { animation-delay: 0.4s; }
.delay3 { animation-delay: 0.6s; }
.delay4 { animation-delay: 0.8s; }
.delay5 { animation-delay: 1.0s; }
.delay6 { animation-delay: 1.2s; }
.delay7 { animation-delay: 1.4s; }

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
