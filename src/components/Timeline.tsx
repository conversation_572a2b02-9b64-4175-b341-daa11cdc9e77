import React from "react";
import { Check } from "lucide-react";
import styles from "./Timeline.module.css";

const timelineEvents = [
  {
    phase: "Study Design & Participant Recruit",
    date: "2024-11-19",
    completed: true,
  },
  {
    phase: "Phase I: Real Life",
    date: "2024-11-20",
    completed: true,
  },
  {
    phase: "Phase II: Holiday Prep",
    date: "2024-11-21",
    completed: true,
  },
  {
    phase: "Phase III: Holiday Experience",
    date: "2024-11-22",
    completed: true,
  },
  {
    phase: "Phase IV: Depth Interviews",
    date: "2024-11-23",
    completed: true,
  },
];

export function Timeline() {
  return (
    <div className={styles.container}>
      <h2 className={styles.title}>Timeline of Events</h2>
      <div className={styles.timelineWrapper}>
        {/* Main timeline line */}
        <div className={styles.timelineLine} />

        {/* Timeline events */}
        <div className={styles.timelineEvents}>
          {timelineEvents.map((event, index) => (
            <div
              key={index}
              className={`${styles.timelineEvent} ${styles.fadeIn} ${styles[`delay${index + 1}`]}`}
            >
              <div className={styles.eventMarker}>
                <div className={styles.completedMarker}>
                  <Check className={styles.checkIcon} />
                </div>
              </div>
              <div className={styles.eventContent}>
                <h3 className={styles.eventTitle}>
                  {event.phase}
                </h3>
                <p className={styles.eventDate}>{event.date}</p>
              </div>
            </div>
          ))}

          {/* Today: Persona Workshop */}
          <div
            className={`${styles.timelineEvent} ${styles.fadeIn} ${styles.delay6}`}
          >
            <div className={styles.eventMarker}>
              <div className={styles.currentMarker} />
            </div>
            <div className={styles.eventContent}>
              <h3 className={styles.eventTitle}>
                Today: Persona Workshop
              </h3>
              <p className={styles.eventDate}>2024-11-24</p>
            </div>
          </div>

          {/* Completion Step */}
          <div
            className={`${styles.timelineEvent} ${styles.fadeIn} ${styles.delay7}`}
          >
            <div className={styles.eventMarker}>
              <div className={styles.futureMarker} />
            </div>
            <div className={styles.eventContent}>
              <h3 className={styles.eventTitle}>Completion</h3>
              <p className={styles.eventDate}>2024-11-25</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
