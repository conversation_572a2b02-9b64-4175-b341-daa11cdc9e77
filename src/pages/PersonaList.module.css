/* PersonaList Page Styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  padding: var(--spacing-4);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

.subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Filter Section */
.filterSection {
  margin-bottom: var(--spacing-8);
}

.filterTitle {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  text-align: center;
}

.filterGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-3);
  max-width: 800px;
  margin: 0 auto;
}

.filterButton {
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  background: white;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-base);
  text-align: center;
}

.filterButton:hover {
  border-color: var(--color-primary-400);
  color: var(--color-primary-600);
}

.filterButtonActive {
  border-color: var(--color-primary-500);
  background: var(--color-primary-500);
  color: white;
}

/* Custom Arrow Styles */
.customArrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding: var(--spacing-2);
  border-radius: var(--radius-full);
  background: white;
  box-shadow: var(--shadow-base);
  border: none;
  cursor: pointer;
  transition: background-color var(--transition-base);
  z-index: 20;
}

.customArrow:hover {
  background: var(--color-gray-200);
}

.customArrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.customArrowPrev {
  left: -64px;
}

.customArrowNext {
  right: -64px;
}

.arrowIcon {
  height: var(--spacing-6);
  width: var(--spacing-6);
  color: var(--color-gray-400);
}

/* Slider Dots - Updated to match Customer Personas carousel dots */
.sliderDots {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-8);
  padding-top: var(--spacing-8);
  justify-content: center;
}

.sliderDot {
  width: var(--spacing-4);
  height: var(--spacing-4);
  border-radius: var(--radius-full);
  border: 2px solid;
  cursor: pointer;
  transition: all 200ms ease-in-out;
  background: transparent;
  border-color: var(--color-gray-300);
}

.sliderDot:focus {
  outline: none;
}

.sliderDot:hover {
  border-color: var(--color-gray-400);
}

/* Active state for slider dots - matches carousel dots */
.sliderDot.slick-active {
  border-color: var(--color-primary-600);
  background: var(--color-primary-600);
}

.sliderDot:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Persona Grid */
.personaGrid {
  display: grid;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

@media (min-width: 640px) {
  .personaGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .personaGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Tips Section */
.tipsSection {
  margin-top: var(--spacing-12);
}

.tipsTitle {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.tipsGrid {
  display: grid;
  gap: var(--spacing-4);
}

@media (min-width: 768px) {
  .tipsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .tipsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.tipCard {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-base);
}

.tipCard:hover {
  box-shadow: var(--shadow-md);
}

.tipIcon {
  width: var(--spacing-8);
  height: var(--spacing-8);
  background: var(--color-primary-100);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-4);
}

.tipIconSvg {
  width: var(--spacing-5);
  height: var(--spacing-5);
  color: var(--color-primary-600);
}

.tipTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

.tipContent {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* Modal Styles */
.modalBackdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--spacing-4);
}

.modal {
  background: #ffffff22;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modalContent {
  padding: var(--spacing-8);
}

.modalCloseButton {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  padding: var(--spacing-2);
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.modalCloseButton:hover {
  background: var(--color-gray-100);
}

.modalCloseIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: var(--color-text-muted);
}

/* Section Titles */
.sectionTitle {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-6);
  text-align: left;
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-8);
}

.loadingSpinner {
  width: var(--spacing-8);
  height: var(--spacing-8);
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Carousel Styles */
.carouselContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-12) 0;
  width: 100%;
  overflow: hidden;
}

.carousel3D {
  position: relative;
  margin: 0 auto;
}

.carouselInner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.7s cubic-bezier(0.77, 0, 0.18, 1);
}

.carouselItem {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carouselItemSelected {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-6);
  border-radius: var(--radius-2xl);
}

.carouselItemRegular {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-6);
  border-radius: var(--radius-2xl);
}

.carouselNavButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding: var(--spacing-3);
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 200ms ease-in-out;
  z-index: 20;
}

.carouselNavButton:hover {
  background: white;
}

.carouselNavLeft {
  left: var(--spacing-8);
}

.carouselNavRight {
  right: var(--spacing-8);
}

.carouselNavIcon {
  height: var(--spacing-6);
  width: var(--spacing-6);
  color: var(--color-gray-600);
}

.carouselDots {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-8);
  padding-top: var(--spacing-8);
}

.carouselDot {
  width: var(--spacing-4);
  height: var(--spacing-4);
  border-radius: var(--radius-full);
  border: 2px solid;
  cursor: pointer;
  transition: all 200ms ease-in-out;
}

.carouselDot:focus {
  outline: none;
}

.carouselDotActive {
  border-color: var(--color-primary-600);
  background: var(--color-primary-600);
}

.carouselDotInactive {
  border-color: var(--color-gray-300);
  background: transparent;
}

.carouselDotInactive:hover {
  border-color: var(--color-gray-400);
}

/* Tips Section */
.tipsContainer {
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-4);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 70px;
}

.tipLabel {
  position: absolute;
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: var(--radius-lg);
  padding: var(--spacing-1) var(--spacing-3);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-sm);
}

.tipContent {
  flex: 1;
  margin-left: var(--spacing-8);
}

.tipTitle {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.tipText {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
}

.tipNavigation {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: var(--spacing-4);
}

.tipButton {
  padding: var(--spacing-2);
  border-radius: var(--radius-full);
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.tipButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tipButtonIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: var(--color-text-muted);
}

.tipCounter {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  margin-top: var(--spacing-2);
}

/* Persona Overview */
.personaOverviewItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Slider Container */
.sliderContainer {
  padding: var(--spacing-8);
  margin: var(--spacing-4) var(--spacing-8);
  position: relative;
}

@media (min-width: 640px) {
  .sliderContainer {
    padding: var(--spacing-10) var(--spacing-8);
  }
}

@media (min-width: 1024px) {
  .sliderContainer {
    padding: var(--spacing-12) var(--spacing-8);
  }
}

.sliderItem {
  padding: var(--spacing-2) var(--spacing-4);
}

/* No Results */
.noResults {
  text-align: center;
  padding: var(--spacing-12) 0;
}

.noResultsIcon {
  margin: 0 auto;
  height: var(--spacing-12);
  width: var(--spacing-12);
  color: var(--color-gray-400);
}

.noResultsTitle {
  margin-top: var(--spacing-4);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.noResultsText {
  margin-top: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-2);
  }

  .title {
    font-size: var(--font-size-3xl);
  }

  .filterGrid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-2);
  }

  .filterButton {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }

  .carouselNavLeft {
    left: var(--spacing-4);
  }

  .carouselNavRight {
    right: var(--spacing-4);
  }

  .sliderContainer {
    padding: var(--spacing-4);
    margin: var(--spacing-2) var(--spacing-4);
  }
}
